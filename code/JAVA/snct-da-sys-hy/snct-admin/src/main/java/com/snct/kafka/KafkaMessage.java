package com.snct.kafka;

public class KafkaMessage {
    private String sn;
    private Integer type;
    private String code;
    private String msg;
    private Integer cost;
    private Long initialTime;

    public KafkaMessage() {
    }

    public KafkaMessage(String msg, Long initialTime) {
        this.msg = msg;
        this.initialTime = initialTime;
    }

    public KafkaMessage(String code, String msg, Integer cost, Long initialTime) {
        this.code = code;
        this.msg = msg;
        this.cost = cost;
        this.initialTime = initialTime;
    }

    public KafkaMessage(Integer type, String code, String msg, Integer cost, Long initialTime) {
        this.type = type;
        this.code = code;
        this.msg = msg;
        this.cost = cost;
        this.initialTime = initialTime;
    }

    public String getSn() {
        return this.sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Integer getType() {
        return this.type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return this.msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getCost() {
        return this.cost;
    }

    public void setCost(Integer cost) {
        this.cost = cost;
    }

    public Long getInitialTime() {
        return this.initialTime;
    }

    public void setInitialTime(Long initialTime) {
        this.initialTime = initialTime;
    }
}
