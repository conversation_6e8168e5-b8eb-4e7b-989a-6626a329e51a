package com.snct.analysis;

import com.snct.common.utils.DateUtils;
import com.snct.dctcore.commoncore.domain.KafkaMessage;
import com.snct.dctcore.commoncore.domain.hbase.PduHbaseVo;
import com.snct.utils.HexUtil2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * PDU数据解析器
 * 类似AttitudeAnalysis的处理模式，统一解析PDU原始数据
 *
 * @author: wzewei
 * @date: 2025-09-05 11:15
 */
public class PduAnalysis {
    protected static Logger logger = LoggerFactory.getLogger(PduAnalysis.class);

    /**
     * 统一解析PDU数据（类似AttitudeAnalysis.getAttitudeList）
     *
     * @param kafkaMessage Kafka消息对象，包含原始数据
     * @return 解析后的PDU HBase对象
     */
    public static PduHbaseVo getPduData(KafkaMessage kafkaMessage) {
        if (kafkaMessage == null || kafkaMessage.getMsg() == null || kafkaMessage.getMsg().isEmpty()) {
            logger.warn("PDU Kafka消息为空");
            return null;
        }

        PduHbaseVo pduHbaseVo = null;

        try {
            // 当前时间
            long currentTime = kafkaMessage.getInitialTime() == null ? System.currentTimeMillis() :
                    kafkaMessage.getInitialTime();

            // 解析原始数据，假设多个数据包用逗号分隔
            String[] rawDataArray = kafkaMessage.getMsg().split(",");
            if (rawDataArray.length == 0) {
                logger.info("PDU原始数据为空: {}", kafkaMessage.getMsg());
                return null;
            }

            // 创建PDU HBase对象
            pduHbaseVo = new PduHbaseVo();
            pduHbaseVo.setInitialTime(DateUtils.fetchWholeSecond(currentTime).toString());
            pduHbaseVo.setInitialBjTime(DateUtils.parseTimeToDate(currentTime, "yyyy-MM-dd HH:mm:ss"));

            // 遍历所有原始数据，统一解析
            for (String hexData : rawDataArray) {
                if (hexData == null || hexData.trim().length() < 4) {
                    continue;
                }

                hexData = hexData.trim();
                String command = hexData.substring(0, 4);

                switch (command) {
                    case "AA0C": // 通道数据
                        parseChannelData(hexData, pduHbaseVo);
                        break;
                    case "AA08": // 系统数据（电流/电压）
                        parseSystemData(hexData, pduHbaseVo);
                        break;
                    case "AA0A": // 总电能
                        parseTotalEnergy(hexData, pduHbaseVo);
                        break;
                    case "AA0E": // 总功率
                        parseTotalPower(hexData, pduHbaseVo);
                        break;
                    default:
                        logger.debug("未知PDU命令类型: {}", command);
                }
            }

            logger.info("PDU数据解析完成");

        } catch (Exception e) {
            logger.error("PDU数据解析失败", e);
            return null;
        }

        return pduHbaseVo;
    }

    /**
     * 解析通道数据 (AA0C)
     */
    private static void parseChannelData(String hexData, PduHbaseVo pduHbaseVo) {
        if (hexData.length() < 22) {
            logger.warn("通道数据格式错误，长度不足: {}", hexData);
            return;
        }

        try {
            // 获取通道号
            String channelStr = hexData.substring(10, 12);
            Integer channel = HexUtil2.hexToInt(channelStr);

            // 获取电流
            String currentStr = hexData.substring(12, 16);
            Integer currentRaw = HexUtil2.hexToInt(currentStr);
            double current = currentRaw / 10.0;

            // 获取功率
            String powerStr = hexData.substring(16, 20);
            Integer powerRaw = HexUtil2.hexToInt(powerStr);
            double power = powerRaw / 10.0;

            // 获取状态
            String statusStr = hexData.substring(20, 22);
            Integer status = HexUtil2.hexToInt(statusStr);

            // 直接设置到PDU对象
            pduHbaseVo.setChannelData(channel,
                String.valueOf(current),
                String.valueOf(power),
                String.valueOf(status));

            logger.debug("解析通道数据 - 通道: {}, 电流: {}A, 功率: {}W, 状态: {}",
                    channel, current, power, status);

        } catch (Exception e) {
            logger.error("解析通道数据失败: {}", hexData, e);
        }
    }

    /**
     * 解析系统数据 (AA08)
     */
    private static void parseSystemData(String hexData, PduHbaseVo pduHbaseVo) {
        if (hexData.length() < 14) {
            logger.warn("系统数据格式错误，长度不足: {}", hexData);
            return;
        }

        try {
            // 获取消息类型
            String typeStr = hexData.substring(6, 8);
            // 获取数值
            String valueStr = hexData.substring(10, 14);
            Integer valueRaw = HexUtil2.hexToInt(valueStr);
            double value = valueRaw / 10.0;

            if ("02".equals(typeStr)) {
                // 输入电流
                pduHbaseVo.setElectric(String.valueOf(value));
                logger.debug("解析系统电流: {}A", value);
            } else if ("03".equals(typeStr)) {
                // 输入电压
                pduHbaseVo.setVoltage(String.valueOf(value));
                logger.debug("解析系统电压: {}V", value);
            }

        } catch (Exception e) {
            logger.error("解析系统数据失败: {}", hexData, e);
        }
    }

    /**
     * 解析总电能数据 (AA0A)
     */
    private static void parseTotalEnergy(String hexData, PduHbaseVo pduHbaseVo) {
        if (hexData.length() < 18) {
            logger.warn("总电能数据格式错误，长度不足: {}", hexData);
            return;
        }

        try {
            // 解析总电能值
            String valueStr = hexData.substring(10, 18);
            Integer valueRaw = HexUtil2.hexToInt(valueStr);

            // 计算总电能实际值：总电能 = valueRaw / (2^16 * 10)
            double totalEnergy = valueRaw / (Math.pow(2, 16) * 10);

            pduHbaseVo.setManage(String.valueOf(totalEnergy));

            logger.debug("解析总电能: {}kWh (原始值: {})", totalEnergy, valueRaw);

        } catch (Exception e) {
            logger.error("解析总电能数据失败: {}", hexData, e);
        }
    }

    /**
     * 解析总功率数据 (AA0E)
     */
    private static void parseTotalPower(String hexData, PduHbaseVo pduHbaseVo) {
        if (hexData.length() < 22) {
            logger.warn("总功率数据格式错误，长度不足: {}", hexData);
            return;
        }

        try {
            // 获取有功功率
            String activePowerStr = hexData.substring(10, 14);
            Integer activePowerRaw = HexUtil2.hexToInt(activePowerStr);
            double activePower = activePowerRaw / 10.0;
            pduHbaseVo.setYesPower(String.valueOf(activePower));

            // 获取无功功率
            String reactivePowerStr = hexData.substring(14, 18);
            Integer reactivePowerRaw = HexUtil2.hexToInt(reactivePowerStr);
            double reactivePower = reactivePowerRaw / 10.0;
            pduHbaseVo.setNoPower(String.valueOf(reactivePower));

            // 获取视在功率
            String apparentPowerStr = hexData.substring(18, 22);
            Integer apparentPowerRaw = HexUtil2.hexToInt(apparentPowerStr);
            double apparentPower = apparentPowerRaw / 10.0;
            pduHbaseVo.setSeePower(String.valueOf(apparentPower));

            // 获取功率因数
            if (hexData.length() >= 24) {
                String powerFactorStr = hexData.substring(22, 24);
                Integer powerFactor = HexUtil2.hexToInt(powerFactorStr);
                pduHbaseVo.setPowerParam(String.valueOf(powerFactor));
            }

            logger.debug("解析总功率 - 有功: {}kW, 无功: {}kW, 视在: {}kW",
                    activePower, reactivePower, apparentPower);

        } catch (Exception e) {
            logger.error("解析总功率数据失败: {}", hexData, e);
        }
    }
}
