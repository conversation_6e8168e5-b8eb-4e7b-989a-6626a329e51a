package com.snct.device.service;

import com.alibaba.fastjson2.JSONObject;
import com.snct.common.constant.CacheConstants;
import com.snct.common.core.redis.RedisCache;
import com.snct.common.enums.KafkaMsgType;

import com.snct.dctcore.commoncore.snmp.SnmpRequest;
import com.snct.dctcore.commoncore.snmp.SnmpResponse;
import com.snct.dctcore.commoncore.snmp.SnmpUtils;
import com.snct.device.domain.Modem;
import com.snct.device.manager.DeviceConnectionManager;
import com.snct.kafka.KafkaMessage;
import com.snct.kafka.KafkaService;

import com.snct.system.domain.Device;
import com.snct.system.domain.data.BuDataModem;
import com.snct.system.domain.msg.BuMsgModem;
import com.snct.system.service.IBuDataModemService;
import com.snct.system.service.IDeviceService;
import com.snct.web.controller.business.DeviceController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Modem设备服务
 * 管理Modem设备的通信和数据处理
 * 
 * <AUTHOR>
 */
@Service
public class ModemDeviceService {
    
    private static final Logger logger = LoggerFactory.getLogger(ModemDeviceService.class);
    
    // Modem设备类型ID (1 猫 2 功放 3 PDU)
    private static final Integer MODEM_DEVICE_TYPE = 1;

    private static final String MODEM_DATA = CacheConstants.DEVICE_DATA_KEY + "modem";
    
    // OID定义 - 去掉前导点
    private static final String OID_SIGNAL = "1.3.6.1.4.1.37576.4.2.1.21.1.4.1"; // 信号强度
    private static final String OID_SPEED = "1.3.6.1.4.1.37576.4.2.1.21.1.1.1"; // 速度
    private static final String OID_SEND_POWER = "1.3.6.1.4.1.37576.3.1.1.9.0"; // 发送功率
    private static final String OID_STATUS = "1.3.6.1.4.1.37576.4.2.1.2.0"; // 状态标志
    
    @Autowired
    private KafkaService kafkaService;
    
    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private DeviceConnectionManager connectionManager;

    @Autowired
    private IBuDataModemService buDataModemService;
    
    // 存储最近查询到的Modem数据
    private final List<BuMsgModem> modemDataList = new ArrayList<>();
    
    /**
     * 获取所有Modem设备数据
     * 从数据库查询所有Modem设备并执行SNMP查询
     */
    public void queryAllModemData() {
        // 从数据库中获取type为52的Modem设备
        Device queryDevice = new Device();
        queryDevice.setType(52L);
        queryDevice.setEnable(1);
        List<Device> modemDevices = deviceService.selectDeviceList(queryDevice);
        
        if (modemDevices == null || modemDevices.isEmpty()) {
            logger.info("未找到可用的Modem设备，跳过SNMP数据采集");
            return;
        }
        
        logger.debug("找到{}个Modem设备需要采集数据", modemDevices.size());
        
        // 清空数据列表
        modemDataList.clear();
        
        // 对每个设备执行SNMP查询
        for (Device modemDevice : modemDevices) {
            // 查询单个设备的SNMP数据
            BuMsgModem modemData = queryModemDevice(modemDevice);
            if (modemData != null) {
                modemDataList.add(modemData);
            }
        }
    }
    
    /**
     * 获取所有已查询的Modem数据
     * 
     * @return Modem数据列表
     */
    public List<BuMsgModem> getAllModemData() {
        return new ArrayList<>(modemDataList);
    }
    
    /**
     * 查询单个Modem设备的SNMP数据
     *
     * @param device 设备对象
     * @return Modem数据对象，查询失败返回null
     */
    private BuMsgModem queryModemDevice(Device device) {
        try {
            logger.debug("开始查询Modem设备[{}]的SNMP数据, IP:{}, 端口:{}", 
                    device.getCode(), device.getIp(), device.getPort());
            
            // 创建SNMP请求，添加所需OID
            SnmpRequest request = new SnmpRequest()
                    .addOid(OID_SIGNAL, "信号强度")
                    .addOid(OID_SPEED, "速度")
                    .addOid(OID_SEND_POWER, "发送功率")
                    .addOid(OID_STATUS, "状态标志");
            
            // 执行SNMP请求
            SnmpResponse response = SnmpUtils.executeRequest(device, request);
            
            // 处理响应
            if (response != null && response.isSuccess()) {
                // 更新设备连接状态为连接
                connectionManager.updateDeviceConnectionStatus(device,
                    DeviceConnectionManager.ConnectionStatus.CONNECTED,
                    DeviceConnectionManager.ProtocolType.SNMP);

                // 创建Modem数据对象
                BuMsgModem modem = new BuMsgModem();
                Modem modem1 = new Modem();
                modem.setStatus(0L);
                
                Map<String, String> allValues = response.getAllValues();
                logger.debug("设备[{}]收到SNMP响应，包含{}个值: {}", 
                        device.getCode(), allValues.size(), allValues);
                
                // 从响应中获取数据并设置到Modem对象
                Double signal = response.getDoubleValue(OID_SIGNAL, 0.0);
                Double speed = response.getDoubleValue(OID_SPEED, 0.0);
                Double sendPower = response.getDoubleValue(OID_SEND_POWER, 0.0);
                Long isFlag = response.getLongValue(OID_STATUS, 0L);
                
                modem.setSignal(signal);
                modem.setSpeed(speed);
                modem.setSendPower(sendPower);
                modem.setIsFlag(isFlag);
                modem.setDeviceId(device.getId());

                modem1.setSignal(signal.toString());
                modem1.setSpeed(speed.toString());
                modem1.setSendPower(sendPower.toString());
                modem1.setIsFlag(isFlag.toString());

                redisCache.setCacheObject(MODEM_DATA, modem1);

                // 保存到数据库中
                BuDataModem buDataModem = new BuDataModem();
                buDataModem.setDeviceId(Math.toIntExact(device.getId()));
                buDataModem.setDeviceCode(device.getCode());
                buDataModem.setDeviceName(device.getName());
                Long initialTime = System.currentTimeMillis();
                buDataModem.setInitialTime(initialTime);
                buDataModem.setInitialBjTime(new Date(initialTime));
                buDataModem.setStatus(0);
                if (signal != null) {
                    buDataModem.setSignal(BigDecimal.valueOf(signal));
                }
                if (speed != null) {
                    buDataModem.setSpeed(BigDecimal.valueOf(speed));
                }
                if (sendPower != null) {
                    buDataModem.setSendPower(BigDecimal.valueOf(sendPower));
                }
                if (isFlag != null) {
                    buDataModem.setIsFlag(Math.toIntExact(isFlag));
                }

                buDataModemService.insertBuDataModem(buDataModem);

                // 记录查询结果
                logger.info("设备[{}]的SNMP查询结果: 信号强度={}, 速度={}, 发送功率={}, 状态标志={}",
                        device.getCode(), modem.getSignal(), modem.getSpeed(), 
                        modem.getSendPower(), modem.getIsFlag());
                
                // 将消息保存到内存中，用于预览
                try {
                    // 获取设备编码
                    String deviceCode = device != null ? device.getCode() : null;
                    // 只有在设备有编码时才可能存放预览数据
                    if (deviceCode != null && !deviceCode.isEmpty()) {
                        // 判断该设备是否开启了预览功能
                        if (DeviceController.viewKey != null && DeviceController.viewKey.containsKey(deviceCode)) {
                            // 获取当前时间并格式化
                            LocalDateTime now = LocalDateTime.now();
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
                            String formattedTime = now.format(formatter);
                            // 构建key: 设备编码###时间
                            String key = deviceCode + "###" + formattedTime;
                            // 将Modem数据简单拼接成字符串保存到viewValue
                            String previewData = "信号强度:" + modem.getSignal() +
                                                 ", 速度:" + modem.getSpeed() +
                                                 ", 发送功率:" + modem.getSendPower() +
                                                 ", 状态标志:" + modem.getIsFlag();
                            DeviceController.viewValue.put(key, previewData);
                        }
                    }
                } catch (Exception e) {
                    logger.error("保存Modem预览数据异常", e);
                }
                
                // 发送到Kafka
                sendToKafka(device, modem);
                
                return modem;
            } else {
                // 更新设备连接状态为断开
                connectionManager.updateDeviceConnectionStatus(device,
                    DeviceConnectionManager.ConnectionStatus.DISCONNECTED,
                    DeviceConnectionManager.ProtocolType.SNMP);

                logger.error("设备[{}]的SNMP查询失败: {}",
                        device.getCode(), response != null ? response.getErrorStatusText() : "无响应");
            }
        } catch (Exception e) {
            // 异常时更新设备连接状态为未知
            connectionManager.updateDeviceConnectionStatus(device,
                DeviceConnectionManager.ConnectionStatus.UNKNOWN,
                DeviceConnectionManager.ProtocolType.SNMP);

            logger.error("设备[{}]的Modem SNMP任务异常", device.getCode(), e);
        }
        return null;
    }
    
    /**
     * 将Modem数据发送到Kafka
     * 
     * @param device 设备对象
     * @param modem Modem数据对象
     */
    private void sendToKafka(Device device, BuMsgModem modem) {
        try {
            KafkaMessage message = new KafkaMessage();
            message.setSn(device.getSn());
            message.setType(KafkaMsgType.MODEM_DEVICE.ordinal());
            message.setCode("MODEM_DATA_" + device.getCode());
            message.setMsg(JSONObject.toJSONString(modem));
            message.setInitialTime(System.currentTimeMillis());
            
            kafkaService.send2Kafka(message);
            logger.info("设备[{}]的Modem数据已发送: {}", device.getCode(), JSONObject.toJSONString(modem));
        } catch (Exception e) {
            logger.error("发送Modem数据到Kafka异常", e);
        }
    }
}
